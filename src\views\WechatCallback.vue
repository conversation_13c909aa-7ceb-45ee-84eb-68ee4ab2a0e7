<template>
  <div class="callback-page">
    <div v-if="loading">处理登录中...</div>
    <div v-if="error">{{ error }}</div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'WechatCallback',
  data() {
    return {
      loading: true,
      error: ''
    }
  },
  created() {
    // 获取URL中的code参数
    const code = this.$route.query.code;
    const state = this.$route.query.state;
    
    if (!code) {
      this.error = '授权失败，未获取到code';
      this.loading = false;
      return;
    }
    
    // 发送code到后端换取用户信息和token
    axios.post('/api/auth/wechat', { code, state })
      .then(response => {
        const { token, user } = response.data;
        // 存储token
        localStorage.setItem('token', token);
        // 存储用户信息
        this.$store.commit('setUser', user);
        // 跳转到首页或指定页面
        this.$router.push('/');
      })
      .catch(error => {
        this.error = '登录失败: ' + (error.response?.data?.message || error.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }
}
</script>