<template>
  <div class="wechat-login">
    <button @click="handleWechatLogin" class="wechat-btn">
      <i class="wechat-icon"></i> 微信登录
    </button>
  </div>
</template>

<script>
export default {
  name: 'WechatLogin',
  data() {
    return {
      appId: 'YOUR_WECHAT_APP_ID', // 替换为您的微信AppID
      redirectUri: encodeURIComponent(window.location.origin + '/auth/wechat/callback')
    }
  },
  methods: {
    handleWechatLogin() {
      // 微信授权链接
      const authUrl = `https://open.weixin.qq.com/connect/qrconnect?appid=${this.appId}&redirect_uri=${this.redirectUri}&response_type=code&scope=snsapi_login&state=${this.generateState()}#wechat_redirect`;
      window.location.href = authUrl;
    },
    generateState() {
      // 生成随机state用于防止CSRF攻击
      return Math.random().toString(36).substring(2);
    }
  }
}
</script>

<style scoped>
/* 样式省略 */
</style>